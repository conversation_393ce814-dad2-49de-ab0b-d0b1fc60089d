<script setup>
import { computed } from 'vue'

// Props
const props = defineProps({
  icon: {
    type: String,
    required: true
  },
  title: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  color: {
    type: String,
    default: 'primary'
  },
  clickable: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['click'])

// 计算属性
const cardClasses = computed(() => ({
  'feature-card': true,
  'feature-card--clickable': props.clickable
}))

// 方法
const handleClick = () => {
  if (props.clickable) {
    emit('click', {
      title: props.title,
      icon: props.icon
    })
  }
}
</script>

<template>
  <div
    :class="cardClasses"
    @click="handleClick"
  >
    <div class="card-content">
      <!-- 图标 -->
      <div class="icon-container">
        <VIcon
          :icon="icon"
          :color="color"
          size="32"
        />
      </div>

      <!-- 标题 -->
      <h3 class="feature-title">
        {{ title }}
      </h3>
    </div>
  </div>
</template>

<style scoped>
.feature-card {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 16px;
  border: 1px solid #e0e0e0;
  background-color: white;
  overflow: hidden;
}

.feature-card--clickable {
  cursor: pointer;
}

.feature-card--clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border-color: #d0d0d0;
}

.card-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 24px 16px;
  height: 100%;
  min-height: 120px;
  justify-content: center;
}

.icon-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
  margin: 0 auto 12px auto;
  border-radius: 50%;
  background-color: #f8f9fa;
  transition: all 0.3s ease;
}

.feature-card--clickable:hover .icon-container {
  background-color: #f0f0f0;
  transform: scale(1.05);
}

.feature-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
  margin: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .card-content {
    padding: 20px 12px;
    min-height: 100px;
  }

  .icon-container {
    width: 56px;
    height: 56px;
    margin-bottom: 10px;
  }

  .feature-title {
    font-size: 0.9rem;
  }
}
</style>
