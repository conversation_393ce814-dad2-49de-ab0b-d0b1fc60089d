<script setup>
import { computed } from 'vue'

// 接收消息数据
const props = defineProps({
  message: {
    type: Object,
    required: true
  }
})

// 计算属性
const isUser = computed(() => props.message.type === 'user')
const isAI = computed(() => props.message.type === 'ai')

// 格式化时间
const formatTime = (timestamp) => {
  const date = new Date(timestamp)
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}
</script>

<template>
  <div class="message-container" :class="{ 'user-message': isUser, 'ai-message': isAI }">
    <div class="message-wrapper">
      <!-- AI消息的头像和标识 -->
      <div v-if="isAI" class="message-avatar">
        <div class="ai-avatar">
          <VIcon icon="mdi-robot" size="20" color="white" />
        </div>
      </div>

      <!-- 消息内容区域 -->
      <div class="message-content">
        <!-- AI消息标题 -->
        <div v-if="isAI" class="message-header">
          <span class="ai-name">Dolphin AI</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>

        <!-- 用户消息标题 -->
        <div v-if="isUser" class="message-header user-header">
          <span class="user-name">你</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>

        <!-- 消息文本 -->
        <div class="message-text" :class="{ 'user-text': isUser, 'ai-text': isAI }">
          {{ message.content }}
        </div>

        <!-- 消息操作按钮 (仅AI消息显示) -->
        <div v-if="isAI" class="message-actions">
          <VBtn
            variant="text"
            size="small"
            icon="mdi-content-copy"
            class="action-btn"
            @click="copyMessage"
          />
          <VBtn
            variant="text"
            size="small"
            icon="mdi-refresh"
            class="action-btn"
            @click="regenerateMessage"
          />
          <VBtn
            variant="text"
            size="small"
            icon="mdi-thumb-up-outline"
            class="action-btn"
            @click="likeMessage"
          />
          <VBtn
            variant="text"
            size="small"
            icon="mdi-thumb-down-outline"
            class="action-btn"
            @click="dislikeMessage"
          />
        </div>
      </div>

      <!-- 用户消息的头像 -->
      <div v-if="isUser" class="message-avatar user-avatar-container">
        <div class="user-avatar">
          <VIcon icon="mdi-account" size="20" color="white" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  methods: {
    copyMessage() {
      navigator.clipboard.writeText(this.message.content)
      console.log('消息已复制')
    },
    regenerateMessage() {
      console.log('重新生成消息')
      // 这里可以添加重新生成消息的逻辑
    },
    likeMessage() {
      console.log('点赞消息')
      // 这里可以添加点赞逻辑
    },
    dislikeMessage() {
      console.log('点踩消息')
      // 这里可以添加点踩逻辑
    }
  }
}
</script>

<style scoped>
.message-container {
  margin-bottom: 24px;
  width: 100%;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 16px;
}

/* AI消息样式 */
.ai-message .message-wrapper {
  justify-content: flex-start;
}

.ai-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 用户消息样式 */
.user-message .message-wrapper {
  justify-content: flex-end;
}

.user-avatar-container {
  order: 2;
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

/* 消息内容区域 */
.message-content {
  flex: 1;
  max-width: calc(100% - 44px);
}

.user-message .message-content {
  order: 1;
  text-align: right;
}

/* 消息头部 */
.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 14px;
}

.user-header {
  justify-content: flex-end;
}

.ai-name, .user-name {
  font-weight: 600;
  color: #333;
}

.message-time {
  color: #666;
  font-size: 12px;
}

/* 消息文本 */
.message-text {
  padding: 12px 16px;
  border-radius: 16px;
  line-height: 1.5;
  font-size: 15px;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.ai-text {
  background-color: #f8f9fa;
  color: #333;
  border: 1px solid #e9ecef;
}

.user-text {
  background-color: #007bff;
  color: white;
  margin-left: auto;
  display: inline-block;
}

/* 消息操作按钮 */
.message-actions {
  display: flex;
  gap: 4px;
  margin-top: 8px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.message-container:hover .message-actions {
  opacity: 1;
}

.action-btn {
  width: 28px;
  height: 28px;
  min-width: 28px;
  color: #666;
}

.action-btn:hover {
  background-color: #f0f0f0;
  color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-wrapper {
    padding: 0 12px;
  }
  
  .message-text {
    font-size: 14px;
    padding: 10px 12px;
  }
  
  .ai-avatar, .user-avatar {
    width: 28px;
    height: 28px;
  }
  
  .message-actions {
    opacity: 1; /* 移动端始终显示操作按钮 */
  }
}
</style>
