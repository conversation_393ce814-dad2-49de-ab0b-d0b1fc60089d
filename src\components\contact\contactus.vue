<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

// 状态管理
const router = useRouter()

// 响应式数据
const contactForm = ref({
  name: '',
  email: '',
  subject: '',
  message: ''
})

const isSubmitting = ref(false)

// 联系方式数据
const contactInfo = [
  {
    icon: 'mdi-email',
    title: '邮箱联系',
    content: '<EMAIL>',
    description: '工作日24小时内回复'
  },
  {
    icon: 'mdi-phone',
    title: '电话咨询',
    content: '************',
    description: '工作时间：9:00-18:00'
  },
  {
    icon: 'mdi-map-marker',
    title: '公司地址',
    content: '北京市朝阳区科技园区',
    description: '欢迎预约参观'
  },
  {
    icon: 'mdi-wechat',
    title: '微信客服',
    content: 'DolphinAI_Support',
    description: '扫码添加客服微信'
  }
]

// 方法
const handleBack = () => {
  router.go(-1)
}

const handleSubmit = async () => {
  if (!contactForm.value.name || !contactForm.value.email || !contactForm.value.message) {
    return
  }

  isSubmitting.value = true
  
  try {
    // 模拟提交表单
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // 重置表单
    contactForm.value = {
      name: '',
      email: '',
      subject: '',
      message: ''
    }
    
    console.log('表单提交成功')
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    isSubmitting.value = false
  }
}
</script>

<template>
  <div class="contact-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <VBtn
        variant="text"
        icon="mdi-arrow-left"
        @click="handleBack"
      />
      <h1 class="page-title">联系我们</h1>
    </div>

    <VContainer class="contact-container">
      <VRow>
        <!-- 联系方式信息 -->
        <VCol cols="12" md="6">
          <VCard class="contact-info-card">
            <VCardTitle>
              <VIcon icon="mdi-information" class="mr-2" />
              联系方式
            </VCardTitle>
            <VCardText>
              <div class="contact-info-list">
                <div
                  v-for="info in contactInfo"
                  :key="info.title"
                  class="contact-info-item"
                >
                  <div class="info-icon">
                    <VIcon :icon="info.icon" size="24" color="primary" />
                  </div>
                  <div class="info-content">
                    <h4 class="info-title">{{ info.title }}</h4>
                    <p class="info-text">{{ info.content }}</p>
                    <p class="info-description">{{ info.description }}</p>
                  </div>
                </div>
              </div>
            </VCardText>
          </VCard>
        </VCol>

        <!-- 联系表单 -->
        <VCol cols="12" md="6">
          <VCard class="contact-form-card">
            <VCardTitle>
              <VIcon icon="mdi-message-text" class="mr-2" />
              发送消息
            </VCardTitle>
            <VCardText>
              <VForm @submit.prevent="handleSubmit">
                <VTextField
                  v-model="contactForm.name"
                  label="姓名 *"
                  variant="outlined"
                  class="mb-4"
                  required
                />
                
                <VTextField
                  v-model="contactForm.email"
                  label="邮箱 *"
                  type="email"
                  variant="outlined"
                  class="mb-4"
                  required
                />
                
                <VTextField
                  v-model="contactForm.subject"
                  label="主题"
                  variant="outlined"
                  class="mb-4"
                />
                
                <VTextarea
                  v-model="contactForm.message"
                  label="消息内容 *"
                  variant="outlined"
                  rows="5"
                  class="mb-4"
                  required
                />
                
                <VBtn
                  type="submit"
                  color="primary"
                  size="large"
                  :loading="isSubmitting"
                  :disabled="!contactForm.name || !contactForm.email || !contactForm.message"
                  block
                >
                  发送消息
                </VBtn>
              </VForm>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>

      <!-- 常见问题 -->
      <VRow class="mt-6">
        <VCol cols="12">
          <VCard class="faq-card">
            <VCardTitle>
              <VIcon icon="mdi-help-circle" class="mr-2" />
              常见问题
            </VCardTitle>
            <VCardText>
              <VExpansionPanels>
                <VExpansionPanel title="如何开始使用Dolphin AI？">
                  <VExpansionPanelText>
                    注册账号后，您可以直接开始与AI助手对话。我们提供详细的使用指南帮助您快速上手。
                  </VExpansionPanelText>
                </VExpansionPanel>
                
                <VExpansionPanel title="数据安全如何保障？">
                  <VExpansionPanelText>
                    我们采用企业级加密技术，确保您的数据安全。所有对话内容都经过加密处理，严格保护用户隐私。
                  </VExpansionPanelText>
                </VExpansionPanel>
                
                <VExpansionPanel title="如何获得技术支持？">
                  <VExpansionPanelText>
                    您可以通过邮件、电话或在线客服获得技术支持。我们的技术团队将在24小时内响应您的问题。
                  </VExpansionPanelText>
                </VExpansionPanel>
              </VExpansionPanels>
            </VCardText>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<style scoped>
.contact-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  gap: 16px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.contact-container {
  padding-top: 24px;
}

.contact-info-card,
.contact-form-card,
.faq-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.contact-info-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.contact-info-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.info-icon {
  flex-shrink: 0;
  margin-top: 4px;
}

.info-content {
  flex: 1;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: #333;
}

.info-text {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #666;
}

.info-description {
  font-size: 12px;
  margin: 0;
  color: #999;
}
</style>
