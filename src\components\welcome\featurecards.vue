<script setup>
import { ref } from 'vue'
import FeatureCard from './featurecard.vue'

// 功能卡片数据
const features = ref([
  {
    id: 1,
    icon: 'mdi-stethoscope',
    title: '医学咨询',
    color: 'primary'
  },
  {
    id: 2,
    icon: 'mdi-heart-pulse',
    title: '心脏超声',
    color: 'red'
  },
  {
    id: 3,
    icon: 'mdi-account-heart',
    title: '妇科检查',
    color: 'pink'
  },
  {
    id: 4,
    icon: 'mdi-shield-check',
    title: '诊断建议',
    color: 'green'
  },
  {
    id: 5,
    icon: 'mdi-chart-line',
    title: '数据分析',
    color: 'orange'
  },
  {
    id: 6,
    icon: 'mdi-school',
    title: '学习资源',
    color: 'purple'
  }
])

// 方法
const handleFeatureClick = (feature) => {
  console.log('点击功能卡片:', feature.title)
  // 这里可以添加具体的功能处理逻辑
  // 比如跳转到对应的功能页面或开始相关对话
}
</script>

<template>
  <div class="feature-cards-container">
    <VContainer>
      <VRow>
        <VCol
          v-for="feature in features"
          :key="feature.id"
          cols="6"
          sm="4"
          md="4"
          lg="2"
          class="feature-col"
        >
          <FeatureCard
            :icon="feature.icon"
            :title="feature.title"
            :color="feature.color"
            @click="handleFeatureClick(feature)"
          />
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<style scoped>
.feature-cards-container {
  padding: 8px 0;
  max-width: 900px;
  margin: 0 auto;
}

.feature-col {
  display: flex;
}

/* 确保卡片高度一致 */
.feature-col :deep(.feature-card) {
  width: 100%;
}

/* 响应式间距调整 */
@media (max-width: 768px) {
  .feature-cards-container {
    padding: 8px 0;
    margin-bottom: 80px; /* 为底部导航留出空间 */
  }
}
</style>
