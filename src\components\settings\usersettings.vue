<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAppStore } from '@/stores/baseStore'

// 状态管理
const router = useRouter()
const appStore = useAppStore()

// 响应式数据
const settings = ref({
  theme: 'light',
  language: 'zh-CN',
  notifications: {
    email: true,
    push: false,
    sound: true
  },
  privacy: {
    showOnlineStatus: true,
    allowDataCollection: false
  }
})

// 方法
const handleBack = () => {
  router.go(-1)
}

const handleSave = () => {
  // 保存设置逻辑
  console.log('保存设置:', settings.value)
  // 这里可以调用API保存设置
}

const handleReset = () => {
  // 重置为默认设置
  settings.value = {
    theme: 'light',
    language: 'zh-CN',
    notifications: {
      email: true,
      push: false,
      sound: true
    },
    privacy: {
      showOnlineStatus: true,
      allowDataCollection: false
    }
  }
}
</script>

<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <VBtn
        variant="text"
        icon="mdi-arrow-left"
        @click="handleBack"
      />
      <h1 class="page-title">系统设置</h1>
    </div>

    <VContainer class="settings-container">
      <VRow>
        <VCol cols="12" md="8" lg="6">
          <VCard class="settings-card">
            <VCardTitle>基本设置</VCardTitle>
            <VCardText>
              <!-- 主题设置 -->
              <div class="setting-group">
                <h3 class="setting-title">外观主题</h3>
                <VRadioGroup v-model="settings.theme" inline>
                  <VRadio label="浅色主题" value="light" />
                  <VRadio label="深色主题" value="dark" />
                  <VRadio label="跟随系统" value="auto" />
                </VRadioGroup>
              </div>

              <VDivider class="my-4" />

              <!-- 语言设置 -->
              <div class="setting-group">
                <h3 class="setting-title">语言设置</h3>
                <VSelect
                  v-model="settings.language"
                  :items="[
                    { title: '简体中文', value: 'zh-CN' },
                    { title: 'English', value: 'en-US' },
                    { title: '繁體中文', value: 'zh-TW' }
                  ]"
                  variant="outlined"
                  density="compact"
                />
              </div>

              <VDivider class="my-4" />

              <!-- 通知设置 -->
              <div class="setting-group">
                <h3 class="setting-title">通知设置</h3>
                <VCheckbox
                  v-model="settings.notifications.email"
                  label="邮件通知"
                  hide-details
                />
                <VCheckbox
                  v-model="settings.notifications.push"
                  label="推送通知"
                  hide-details
                />
                <VCheckbox
                  v-model="settings.notifications.sound"
                  label="声音提醒"
                  hide-details
                />
              </div>

              <VDivider class="my-4" />

              <!-- 隐私设置 -->
              <div class="setting-group">
                <h3 class="setting-title">隐私设置</h3>
                <VCheckbox
                  v-model="settings.privacy.showOnlineStatus"
                  label="显示在线状态"
                  hide-details
                />
                <VCheckbox
                  v-model="settings.privacy.allowDataCollection"
                  label="允许数据收集用于改进服务"
                  hide-details
                />
              </div>
            </VCardText>

            <!-- 操作按钮 -->
            <VCardActions class="justify-end">
              <VBtn
                variant="outlined"
                @click="handleReset"
              >
                重置默认
              </VBtn>
              <VBtn
                color="primary"
                @click="handleSave"
              >
                保存设置
              </VBtn>
            </VCardActions>
          </VCard>
        </VCol>
      </VRow>
    </VContainer>
  </div>
</template>

<style scoped>
.settings-page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.page-header {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e0e0e0;
  gap: 16px;
}

.page-title {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
}

.settings-container {
  padding-top: 24px;
}

.settings-card {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-group {
  margin-bottom: 16px;
}

.setting-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 12px;
  color: #333;
}

.v-checkbox {
  margin-bottom: 8px;
}
</style>
