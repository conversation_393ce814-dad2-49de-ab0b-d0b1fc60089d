<script setup>
import { computed } from 'vue'
import { useChatStore } from '@/stores/baseStore'
import WelcomeSection from '@/components/welcome/welcomesection.vue'
import FeatureCards from '@/components/welcome/featurecards.vue'
import ChatMessage from '@/components/chat/chatmessage.vue'

// 使用聊天store
const chatStore = useChatStore()

// 计算属性
const showWelcome = computed(() => {
  // 显示欢迎页面的条件：
  // 1. showWelcome为true，或者
  // 2. 当前对话没有消息
  return chatStore.showWelcome || chatStore.currentMessages.length === 0
})
const chatMessages = computed(() => chatStore.currentMessages)

// 方法
const handleStartChat = () => {
  chatStore.setShowWelcome(false)
  console.log('开始聊天')
}

const handleBackToWelcome = () => {
  chatStore.createNewConversation()
  console.log('返回欢迎页面')
}
</script>

<template>
  <div class="main-content">
    <!-- 欢迎页面 -->
    <div
      v-if="showWelcome"
      class="welcome-page"
    >
      <WelcomeSection />
      <FeatureCards />
    </div>

    <!-- 聊天页面 -->
    <div
      v-else
      class="chat-page"
    >
      <!-- 聊天消息区域 -->
      <div class="chat-messages">
        <VContainer>
          <div
            v-if="chatMessages.length === 0"
            class="empty-chat"
          >
            <VBtn
              variant="outlined"
              prepend-icon="mdi-arrow-left"
              @click="handleBackToWelcome"
            >
              返回首页
            </VBtn>
          </div>

          <!-- 显示聊天消息 -->
          <div
            v-for="message in chatMessages"
            :key="message.id"
            class="message-item"
          >
            <ChatMessage :message="message" />
          </div>

          <!-- 加载状态指示器 -->
          <div
            v-if="chatStore.isLoading"
            class="loading-indicator"
          >
            <div class="loading-message">
              <div class="ai-avatar-small">
                <VIcon icon="mdi-robot" size="16" color="white" />
              </div>
              <div class="typing-animation">
                <span class="typing-text">Dolphin AI 正在思考</span>
                <div class="typing-dots">
                  <span></span>
                  <span></span>
                  <span></span>
                </div>
              </div>
            </div>
          </div>
        </VContainer>
      </div>
    </div>
  </div>
</template>

<style scoped>
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  background-color: rgb(var(--v-theme-surface));
  padding-bottom: 120px;
}

.welcome-page {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 100%;
  padding: 16px 0;
}

.chat-page {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  padding: 24px 0;
}

.empty-chat {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

.message-item {
  margin-bottom: 0; /* ChatMessage组件内部已有margin */
}

/* 加载指示器样式 */
.loading-indicator {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.loading-message {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 800px;
  width: 100%;
  padding: 0 16px;
}

.ai-avatar-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.typing-animation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.typing-text {
  color: #666;
  font-size: 14px;
}

.typing-dots {
  display: flex;
  gap: 4px;
}

.typing-dots span {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  background-color: #666;
  animation: typing 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-dots span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 美化主内容区域滚动条 - 与侧边栏保持一致 */
.main-content::-webkit-scrollbar {
  width: 6px;
}

.main-content::-webkit-scrollbar-track {
  background: transparent;
}

.main-content::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
  transition: all 0.3s ease;
}

.main-content:hover::-webkit-scrollbar-thumb {
  background: rgba(0, 0, 0, 0.2);
}

.main-content::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 0, 0, 0.3);
}

.main-content::-webkit-scrollbar-thumb:active {
  background: rgba(0, 0, 0, 0.4);
}

/* Firefox滚动条样式 */
.main-content {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.1) transparent;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-content {
    padding-bottom: 80px; /* 为底部导航留出空间 */
  }

  .welcome-page {
    padding: 8px 0;
  }

  .chat-messages {
    padding: 12px 0;
  }

  /* 移动端滚动条更细 */
  .main-content::-webkit-scrollbar {
    width: 4px;
  }
}
</style>
